# Admin Panel 2FA API Dokümantasyonu

Bu dokümantasyon, Strapi Admin Paneli için e-posta OTP tabanlı 2FA (Two-Factor Authentication) sisteminin API endpoint'lerini açıklar.

## Genel Bilgiler

- **Base URL**: `{STRAPI_URL}/api`
- **Authentication**: JWT <PERSON> (Step 2 sonrası)
- **Content-Type**: `application/json`

## Authentication Flow

Admin paneline giriş 2 adımda gerçekleşir:

1. **Step 1**: Email/Password doğrulama → OTP gönderimi
2. **Step 2**: OTP doğrulama → JWT token alma

## API Endpoints

### 1. Login Step 1 - Email/Password Doğrulama

**Endpoint**: `POST /api/admin/auth/login-step1`

**Açıklama**: Admin kullanıcısının email ve şifresini doğrular, başarılı olursa OTP kodu e-posta ile gönderir.

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "your_password"
}
```

**Success Response (200)**:
```json
{
  "success": true,
  "requiresOtp": true,
  "message": "OTP kodu e-posta adresinize gönderildi",
  "tempToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresIn": 300
}
```

**Success Response - 2FA Disabled (200)**:
```json
{
  "success": true,
  "requiresOtp": false,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firstname": "John",
    "lastname": "Doe",
    "username": "admin",
    "isActive": true,
    "roles": [...]
  }
}
```

**Error Responses**:
- `400`: Email ve şifre gereklidir
- `400`: Geçersiz email veya şifre
- `400`: Çok sık OTP talebi. Lütfen 1 dakika bekleyin.
- `500`: OTP oluşturulurken hata oluştu
- `500`: OTP e-postası gönderilemedi

### 2. Verify OTP - OTP Doğrulama

**Endpoint**: `POST /api/admin/auth/verify-otp`

**Açıklama**: Kullanıcının girdiği OTP kodunu doğrular ve başarılı olursa JWT token döner.

**Request Body**:
```json
{
  "otpCode": "123456",
  "tempToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Success Response (200)**:
```json
{
  "success": true,
  "message": "Giriş başarılı",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firstname": "John",
    "lastname": "Doe",
    "username": "admin",
    "isActive": true,
    "roles": [...]
  }
}
```

**Error Responses**:
- `400`: OTP kodu ve geçici token gereklidir
- `400`: Geçersiz veya süresi dolmuş oturum
- `400`: Kullanıcı bulunamadı
- `400`: Geçersiz veya süresi dolmuş OTP kodu
- `400`: Kullanıcı hesabı aktif değil
- `500`: OTP doğrulama sırasında hata oluştu

### 3. Resend OTP - OTP Yeniden Gönderme

**Endpoint**: `POST /api/admin/auth/resend-otp`

**Açıklama**: Yeni bir OTP kodu oluşturur ve e-posta ile gönderir.

**Request Body**:
```json
{
  "tempToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Success Response (200)**:
```json
{
  "success": true,
  "message": "Yeni OTP kodu e-posta adresinize gönderildi",
  "expiresIn": 300
}
```

**Error Responses**:
- `400`: Geçici token gereklidir
- `400`: Geçersiz veya süresi dolmuş oturum
- `400`: Kullanıcı hesabı aktif değil
- `400`: Çok sık OTP talebi. Lütfen 1 dakika bekleyin.
- `500`: OTP oluşturulurken hata oluştu
- `500`: OTP e-postası gönderilemedi

## Frontend Entegrasyon Örneği

### JavaScript/React Örneği

```javascript
class AdminAuth {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    this.tempToken = null;
    this.authToken = null;
  }

  async loginStep1(email, password) {
    const response = await fetch(`${this.baseUrl}/api/admin/auth/login-step1`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    });

    const data = await response.json();
    
    if (data.success && data.requiresOtp) {
      this.tempToken = data.tempToken;
      return { requiresOtp: true, message: data.message };
    } else if (data.success && !data.requiresOtp) {
      this.authToken = data.token;
      return { requiresOtp: false, user: data.user, token: data.token };
    }
    
    throw new Error(data.message || 'Login failed');
  }

  async verifyOtp(otpCode) {
    if (!this.tempToken) {
      throw new Error('No temporary token available');
    }

    const response = await fetch(`${this.baseUrl}/api/admin/auth/verify-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        otpCode, 
        tempToken: this.tempToken 
      }),
    });

    const data = await response.json();
    
    if (data.success) {
      this.authToken = data.token;
      this.tempToken = null;
      return { user: data.user, token: data.token };
    }
    
    throw new Error(data.message || 'OTP verification failed');
  }

  async resendOtp() {
    if (!this.tempToken) {
      throw new Error('No temporary token available');
    }

    const response = await fetch(`${this.baseUrl}/api/admin/auth/resend-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        tempToken: this.tempToken 
      }),
    });

    const data = await response.json();
    
    if (data.success) {
      return { message: data.message };
    }
    
    throw new Error(data.message || 'Resend OTP failed');
  }

  // Admin API çağrıları için kullanılacak
  getAuthHeaders() {
    return {
      'Authorization': `Bearer ${this.authToken}`,
      'Content-Type': 'application/json',
    };
  }
}

// Kullanım örneği
const auth = new AdminAuth('https://your-strapi-url.com');

// Step 1: Email/Password
try {
  const result = await auth.loginStep1('<EMAIL>', 'password');
  
  if (result.requiresOtp) {
    // OTP input ekranını göster
    console.log(result.message);
  } else {
    // Direkt giriş başarılı (2FA kapalı)
    console.log('Login successful:', result.user);
  }
} catch (error) {
  console.error('Login failed:', error.message);
}

// Step 2: OTP Verification
try {
  const result = await auth.verifyOtp('123456');
  console.log('Login successful:', result.user);
} catch (error) {
  console.error('OTP verification failed:', error.message);
}
```

## Güvenlik Notları

1. **Rate Limiting**: OTP talepleri 1 dakika aralıklarla sınırlıdır
2. **OTP Geçerlilik**: OTP kodları 5 dakika geçerlidir
3. **Temp Token**: Geçici tokenlar 5 dakika geçerlidir
4. **JWT Token**: Ana JWT tokenlar 30 gün geçerlidir
5. **HTTPS**: Tüm API çağrıları HTTPS üzerinden yapılmalıdır
6. **Token Storage**: Tokenları güvenli şekilde saklayın (httpOnly cookies önerilir)

## Hata Kodları

- `400`: Bad Request - Geçersiz istek parametreleri
- `401`: Unauthorized - Kimlik doğrulama hatası
- `500`: Internal Server Error - Sunucu hatası

## Test Ortamı

Test ortamında 2FA'yı devre dışı bırakmak için admin user'ın `twoFactorEnabled` alanını `false` yapabilirsiniz.
