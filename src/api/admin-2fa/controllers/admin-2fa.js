'use strict';

const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

/**
 * Admin 2FA Controller
 */
module.exports = {
  /**
   * Step 1: Validate email and password, send OTP
   */
  async loginStep1(ctx) {
    const { email, password } = ctx.request.body;

    // Validate input
    if (!email || !password) {
      return ctx.badRequest('Email ve şifre gereklidir');
    }

    try {
      // Find admin user by email
      const adminUser = await strapi.query('admin::user').findOne({
        where: { 
          email: email.toLowerCase(),
          isActive: true,
          blocked: false
        },
        populate: ['roles']
      });

      if (!adminUser) {
        return ctx.badRequest('Geçersiz email veya şifre');
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, adminUser.password);
      if (!isPasswordValid) {
        return ctx.badRequest('Geçersiz email veya şifre');
      }

      // Check if 2FA is enabled for this user
      if (!adminUser.twoFactorEnabled) {
        // If 2FA is disabled, proceed with normal login (fallback)
        const token = this.generateAdminJwt(adminUser);
        return ctx.send({
          success: true,
          requiresOtp: false,
          token,
          user: this.sanitizeAdminUser(adminUser)
        });
      }

      // Get OTP service
      const otpService = strapi.service('api::admin-2fa.otp');

      // Check rate limiting
      const userData = await otpService.getUserOtpData(adminUser.id);
      if (userData && !otpService.canRequestNewOtp(userData.lastOtpSentAt)) {
        return ctx.badRequest('Çok sık OTP talebi. Lütfen 1 dakika bekleyin.');
      }

      // Generate OTP
      const otpCode = otpService.generateOtpCode();
      const otpExpiration = otpService.generateOtpExpiration();

      // Store OTP data
      const otpStored = await otpService.storeOtpData(adminUser.id, otpCode, otpExpiration);
      if (!otpStored) {
        return ctx.internalServerError('OTP oluşturulurken hata oluştu');
      }

      // Send OTP email
      const emailSent = await otpService.sendOtpEmail(
        adminUser.email, 
        otpCode, 
        `${adminUser.firstname} ${adminUser.lastname}`.trim()
      );

      if (!emailSent) {
        return ctx.internalServerError('OTP e-postası gönderilemedi');
      }

      // Generate temporary session token for step 2
      const tempToken = this.generateTempSessionToken(adminUser.id);

      return ctx.send({
        success: true,
        requiresOtp: true,
        message: 'OTP kodu e-posta adresinize gönderildi',
        tempToken,
        expiresIn: 300 // 5 minutes
      });

    } catch (error) {
      strapi.log.error('Login Step 1 error:', error);
      return ctx.internalServerError('Giriş işlemi sırasında hata oluştu');
    }
  },

  /**
   * Step 2: Verify OTP and complete login
   */
  async verifyOtp(ctx) {
    const { otpCode, tempToken } = ctx.request.body;

    // Validate input
    if (!otpCode || !tempToken) {
      return ctx.badRequest('OTP kodu ve geçici token gereklidir');
    }

    try {
      // Verify temporary token
      const decoded = this.verifyTempSessionToken(tempToken);
      if (!decoded) {
        return ctx.badRequest('Geçersiz veya süresi dolmuş oturum');
      }

      const userId = decoded.userId;

      // Get user and OTP data
      const otpService = strapi.service('api::admin-2fa.otp');
      const userData = await otpService.getUserOtpData(userId);

      if (!userData) {
        return ctx.badRequest('Kullanıcı bulunamadı');
      }

      // Validate OTP
      const isOtpValid = otpService.validateOtpCode(
        otpCode,
        userData.otpCode,
        userData.otpExpiresAt
      );

      if (!isOtpValid) {
        return ctx.badRequest('Geçersiz veya süresi dolmuş OTP kodu');
      }

      // Get full user data for token generation
      const adminUser = await strapi.query('admin::user').findOne({
        where: { id: userId },
        populate: ['roles']
      });

      if (!adminUser || !adminUser.isActive || adminUser.blocked) {
        return ctx.badRequest('Kullanıcı hesabı aktif değil');
      }

      // Clear OTP data
      await otpService.clearOtpData(userId);

      // Generate final JWT token
      const token = this.generateAdminJwt(adminUser);

      return ctx.send({
        success: true,
        message: 'Giriş başarılı',
        token,
        user: this.sanitizeAdminUser(adminUser)
      });

    } catch (error) {
      strapi.log.error('OTP verification error:', error);
      return ctx.internalServerError('OTP doğrulama sırasında hata oluştu');
    }
  },

  /**
   * Resend OTP code
   */
  async resendOtp(ctx) {
    const { tempToken } = ctx.request.body;

    if (!tempToken) {
      return ctx.badRequest('Geçici token gereklidir');
    }

    try {
      // Verify temporary token
      const decoded = this.verifyTempSessionToken(tempToken);
      if (!decoded) {
        return ctx.badRequest('Geçersiz veya süresi dolmuş oturum');
      }

      const userId = decoded.userId;

      // Get user data
      const adminUser = await strapi.query('admin::user').findOne({
        where: { id: userId }
      });

      if (!adminUser || !adminUser.isActive || adminUser.blocked) {
        return ctx.badRequest('Kullanıcı hesabı aktif değil');
      }

      // Get OTP service
      const otpService = strapi.service('api::admin-2fa.otp');

      // Check rate limiting
      const userData = await otpService.getUserOtpData(userId);
      if (userData && !otpService.canRequestNewOtp(userData.lastOtpSentAt)) {
        return ctx.badRequest('Çok sık OTP talebi. Lütfen 1 dakika bekleyin.');
      }

      // Generate new OTP
      const otpCode = otpService.generateOtpCode();
      const otpExpiration = otpService.generateOtpExpiration();

      // Store OTP data
      const otpStored = await otpService.storeOtpData(userId, otpCode, otpExpiration);
      if (!otpStored) {
        return ctx.internalServerError('OTP oluşturulurken hata oluştu');
      }

      // Send OTP email
      const emailSent = await otpService.sendOtpEmail(
        adminUser.email,
        otpCode,
        `${adminUser.firstname} ${adminUser.lastname}`.trim()
      );

      if (!emailSent) {
        return ctx.internalServerError('OTP e-postası gönderilemedi');
      }

      return ctx.send({
        success: true,
        message: 'Yeni OTP kodu e-posta adresinize gönderildi',
        expiresIn: 300 // 5 minutes
      });

    } catch (error) {
      strapi.log.error('Resend OTP error:', error);
      return ctx.internalServerError('OTP yeniden gönderme sırasında hata oluştu');
    }
  },

  /**
   * Generate temporary session token for OTP verification
   */
  generateTempSessionToken(userId) {
    const payload = {
      userId,
      type: 'temp_session',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (5 * 60) // 5 minutes
    };

    return jwt.sign(payload, strapi.config.get('admin.auth.secret'));
  },

  /**
   * Verify temporary session token
   */
  verifyTempSessionToken(token) {
    try {
      const decoded = jwt.verify(token, strapi.config.get('admin.auth.secret'));
      if (decoded.type !== 'temp_session') {
        return null;
      }
      return decoded;
    } catch (error) {
      return null;
    }
  },

  /**
   * Generate admin JWT token
   */
  generateAdminJwt(adminUser) {
    const payload = {
      id: adminUser.id,
      email: adminUser.email,
      firstname: adminUser.firstname,
      lastname: adminUser.lastname,
      username: adminUser.username,
      isActive: adminUser.isActive,
      roles: adminUser.roles
    };

    return jwt.sign(payload, strapi.config.get('admin.auth.secret'), {
      expiresIn: '30d'
    });
  },

  /**
   * Sanitize admin user data for response
   */
  sanitizeAdminUser(adminUser) {
    const { password, otpCode, otpExpiresAt, resetPasswordToken, registrationToken, ...sanitized } = adminUser;
    return sanitized;
  }
};
