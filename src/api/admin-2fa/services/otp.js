'use strict';

const crypto = require('crypto');

/**
 * OTP Service for Admin 2FA
 */
module.exports = () => ({
  /**
   * Generate a 6-digit OTP code
   * @returns {string} 6-digit OTP code
   */
  generateOtpCode() {
    return crypto.randomInt(100000, 999999).toString();
  },

  /**
   * Generate OTP expiration time (5 minutes from now)
   * @returns {Date} Expiration date
   */
  generateOtpExpiration() {
    const expirationTime = new Date();
    expirationTime.setMinutes(expirationTime.getMinutes() + 5);
    return expirationTime;
  },

  /**
   * Validate OTP code
   * @param {string} inputCode - User provided OTP code
   * @param {string} storedCode - Stored OTP code
   * @param {Date} expiresAt - OTP expiration time
   * @returns {boolean} True if valid, false otherwise
   */
  validateOtpCode(inputCode, storedCode, expiresAt) {
    if (!inputCode || !storedCode || !expiresAt) {
      return false;
    }

    // Check if OTP has expired
    if (new Date() > new Date(expiresAt)) {
      return false;
    }

    // Check if codes match
    return inputCode.toString() === storedCode.toString();
  },

  /**
   * Check if user can request new OTP (rate limiting)
   * @param {Date} lastOtpSentAt - Last OTP sent time
   * @returns {boolean} True if can request, false otherwise
   */
  canRequestNewOtp(lastOtpSentAt) {
    if (!lastOtpSentAt) {
      return true;
    }

    const now = new Date();
    const lastSent = new Date(lastOtpSentAt);
    const timeDifference = now - lastSent;
    const oneMinuteInMs = 60 * 1000;

    // Allow new OTP request only after 1 minute
    return timeDifference >= oneMinuteInMs;
  },

  /**
   * Send OTP via email
   * @param {string} email - User email
   * @param {string} otpCode - OTP code to send
   * @param {string} userName - User name for personalization
   * @returns {Promise<boolean>} True if sent successfully
   */
  async sendOtpEmail(email, otpCode, userName = '') {
    try {
      const emailService = strapi.plugins.email.services.email;
      
      const emailTemplate = {
        to: email,
        from: strapi.config.get('plugins.email.settings.defaultFrom'),
        subject: 'Admin Panel - Güvenlik Kodu',
        html: this.generateOtpEmailTemplate(otpCode, userName),
        text: `Merhaba ${userName},\n\nAdmin paneline giriş için güvenlik kodunuz: ${otpCode}\n\nBu kod 5 dakika geçerlidir.\n\nCruise Collective Ekibi`
      };

      await emailService.send(emailTemplate);
      return true;
    } catch (error) {
      strapi.log.error('OTP email sending failed:', error);
      return false;
    }
  },

  /**
   * Generate HTML email template for OTP
   * @param {string} otpCode - OTP code
   * @param {string} userName - User name
   * @returns {string} HTML email template
   */
  generateOtpEmailTemplate(otpCode, userName) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Admin Panel - Güvenlik Kodu</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #007bff; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px; background-color: #f9f9f9; }
          .otp-code { 
            font-size: 32px; 
            font-weight: bold; 
            color: #007bff; 
            text-align: center; 
            padding: 20px; 
            background-color: white; 
            border: 2px dashed #007bff; 
            margin: 20px 0; 
            letter-spacing: 5px;
          }
          .warning { color: #dc3545; font-weight: bold; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Cruise Collective Admin</h1>
          </div>
          <div class="content">
            <h2>Merhaba ${userName || 'Admin'},</h2>
            <p>Admin paneline giriş yapmak için aşağıdaki güvenlik kodunu kullanın:</p>
            
            <div class="otp-code">${otpCode}</div>
            
            <p class="warning">⚠️ Bu kod 5 dakika geçerlidir.</p>
            <p>Eğer bu giriş denemesi sizin tarafınızdan yapılmadıysa, lütfen derhal sistem yöneticinizle iletişime geçin.</p>
            
            <p>Güvenliğiniz için:</p>
            <ul>
              <li>Bu kodu kimseyle paylaşmayın</li>
              <li>Şüpheli aktivite durumunda hemen bildirin</li>
              <li>Güvenli bir ağ bağlantısı kullanın</li>
            </ul>
          </div>
          <div class="footer">
            <p>Bu e-posta otomatik olarak gönderilmiştir. Lütfen yanıtlamayın.</p>
            <p>&copy; 2024 Cruise Collective. Tüm hakları saklıdır.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  },

  /**
   * Clear OTP data from user
   * @param {number} userId - Admin user ID
   * @returns {Promise<boolean>} True if cleared successfully
   */
  async clearOtpData(userId) {
    try {
      await strapi.query('admin::user').update({
        where: { id: userId },
        data: {
          otpCode: null,
          otpExpiresAt: null,
          lastOtpSentAt: null
        }
      });
      return true;
    } catch (error) {
      strapi.log.error('Failed to clear OTP data:', error);
      return false;
    }
  },

  /**
   * Store OTP data for user
   * @param {number} userId - Admin user ID
   * @param {string} otpCode - Generated OTP code
   * @param {Date} expiresAt - OTP expiration time
   * @returns {Promise<boolean>} True if stored successfully
   */
  async storeOtpData(userId, otpCode, expiresAt) {
    try {
      await strapi.query('admin::user').update({
        where: { id: userId },
        data: {
          otpCode,
          otpExpiresAt: expiresAt,
          lastOtpSentAt: new Date()
        }
      });
      return true;
    } catch (error) {
      strapi.log.error('Failed to store OTP data:', error);
      return false;
    }
  },

  /**
   * Get user OTP data
   * @param {number} userId - Admin user ID
   * @returns {Promise<Object|null>} User OTP data or null
   */
  async getUserOtpData(userId) {
    try {
      const user = await strapi.query('admin::user').findOne({
        where: { id: userId },
        select: ['otpCode', 'otpExpiresAt', 'lastOtpSentAt', 'twoFactorEnabled']
      });
      return user;
    } catch (error) {
      strapi.log.error('Failed to get user OTP data:', error);
      return null;
    }
  }
});
