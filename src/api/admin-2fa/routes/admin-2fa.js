module.exports = {
  routes: [
    {
      method: "POST",
      path: "/admin/auth/login-step1",
      handler: "admin-2fa.loginStep1",
      config: {
        auth: false,
        policies: [],
        middlewares: []
      },
    },
    {
      method: "POST", 
      path: "/admin/auth/verify-otp",
      handler: "admin-2fa.verifyOtp",
      config: {
        auth: false,
        policies: [],
        middlewares: []
      },
    },
    {
      method: "POST",
      path: "/admin/auth/resend-otp", 
      handler: "admin-2fa.resendOtp",
      config: {
        auth: false,
        policies: [],
        middlewares: []
      },
    }
  ],
};
