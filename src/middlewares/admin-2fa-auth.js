"use strict";

const jwt = require("jsonwebtoken");

/**
 * Admin 2FA Authentication Middleware
 * This middleware only protects the admin panel UI, not API routes
 */
module.exports = (config, { strapi }) => {
  return async (ctx, next) => {
    // Only apply to admin panel UI routes (not API routes)
    if (!ctx.request.url.startsWith("/admin")) {
      return await next();
    }

    // Skip authentication for public admin routes
    const publicAdminRoutes = [
      "/admin/init",
      "/admin/registration-info",
      "/admin/forgot-password",
      "/admin/reset-password",
      "/admin/login",
      "/admin/auth/login",
    ];

    // Skip for public admin routes
    if (publicAdminRoutes.some((route) => ctx.request.url.startsWith(route))) {
      return await next();
    }

    // Skip for static assets
    if (
      ctx.request.url.includes("/admin/assets/") ||
      ctx.request.url.includes("/admin/favicon.ico") ||
      ctx.request.url.includes("/admin/manifest.json") ||
      ctx.request.url.includes("/admin/runtime~") ||
      ctx.request.url.includes("/admin/main.") ||
      ctx.request.url.match(
        /\/admin\/.*\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/
      )
    ) {
      return await next();
    }

    try {
      // Get authorization header
      const authorization = ctx.request.header.authorization;

      if (!authorization) {
        return ctx.unauthorized("Authorization header is required");
      }

      // Extract token
      const token = authorization.replace("Bearer ", "");

      if (!token) {
        return ctx.unauthorized("Token is required");
      }

      // Verify JWT token
      let decoded;
      try {
        decoded = jwt.verify(token, strapi.config.get("admin.auth.secret"));
      } catch (jwtError) {
        return ctx.unauthorized("Invalid or expired token");
      }

      // Check if it's a temporary session token (should not be allowed for admin access)
      if (decoded.type === "temp_session") {
        return ctx.unauthorized("2FA verification required");
      }

      // Verify user exists and is active
      const adminUser = await strapi.query("admin::user").findOne({
        where: {
          id: decoded.id,
          isActive: true,
          blocked: false,
        },
        populate: ["roles"],
      });

      if (!adminUser) {
        return ctx.unauthorized("User not found or inactive");
      }

      // Check if 2FA is enabled and enforce it
      if (adminUser.twoFactorEnabled !== false) {
        // Default to true if not set
        // For admin users with 2FA enabled, ensure the token contains proper authentication
        if (!decoded.id || !decoded.email) {
          return ctx.unauthorized("Invalid authentication token");
        }
      }

      // Add user to context for use in controllers
      ctx.state.admin = {
        id: adminUser.id,
        email: adminUser.email,
        firstname: adminUser.firstname,
        lastname: adminUser.lastname,
        username: adminUser.username,
        roles: adminUser.roles,
        isActive: adminUser.isActive,
        twoFactorEnabled: adminUser.twoFactorEnabled,
      };

      // Continue to next middleware
      await next();
    } catch (error) {
      strapi.log.error("Admin 2FA Auth Middleware Error:", error);
      return ctx.internalServerError("Authentication error");
    }
  };
};
